import { EventContext } from '@cloudflare/workers-types';
import { Env } from '../types';
import { drizzle } from 'drizzle-orm/d1';
import { newaccountsTable } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import moment from 'moment-timezone';

/**
 * 每天早上8点执行的重置账号任务
 * 获取 resetStatus==0 && initStatus==0 的账号，取十个账号
 * 然后将账号发送给重置请求
 */
export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const env = context.env as Env;
    const db = drizzle(env.DB);

    try {
        // 获取当前时间
        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

        console.log(`[Reset Accounts Cron] Starting execution at ${currentTime}`);

        // 查询符合条件的账号：resetStatus==0 && initStatus==0
        const accounts = await db.select()
            .from(newaccountsTable)
            .where(and(
                eq(newaccountsTable.resetStatus, 0),
                eq(newaccountsTable.initStatus, 0)
            ))
            .limit(10);

        if (accounts.length === 0) {
            return new Response(JSON.stringify({
                success: true,
                message: 'No accounts found matching criteria (resetStatus=0 && initStatus=0)',
                count: 0,
                timestamp: currentTime
            }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            });
        }

 

        // 发送请求到重置账号API
        const resetResponse = await fetch('https://godmailyi-msaccres.hf.space/reset-account', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Seedlog-Cron/1.0'
            },
            body: JSON.stringify(accounts)
        });

        const resetResult = await resetResponse.text();
        const isSuccess = resetResponse.ok;

        // 记录执行结果
        const logMessage = isSuccess 
            ? `Successfully sent ${accounts.length} accounts to reset API`
            : `Failed to send accounts to reset API: ${resetResult}`;

        console.log(`[Reset Accounts Cron] ${logMessage}`, {
            timestamp: currentTime,
            accountCount: accounts.length,
            accountEmails: accounts.map(a => a.email),
            resetApiStatus: resetResponse.status,
            resetApiResponse: resetResult
        });

        return new Response(JSON.stringify({
            success: isSuccess,
            message: logMessage,
            accountCount: accounts.length,
            accountEmails: accounts.map(a => a.email),
            resetApiStatus: resetResponse.status,
            resetApiResponse: isSuccess ? 'Success' : resetResult,
            timestamp: currentTime
        }), {
            status: isSuccess ? 200 : 500,
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        const currentTime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
        
        console.error('[Reset Accounts Cron] Error:', error);

        return new Response(JSON.stringify({
            success: false,
            error: errorMessage,
            timestamp: currentTime
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
};
